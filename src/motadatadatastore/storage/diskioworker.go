/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	For reading IO worker is in charge of reading; read-related requests arrive here, and the requests are then executed.
*/

package storage

import (
	"errors"
	"fmt"
	"github.com/motadata2025/gouring"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"runtime"
	"sync"
	"syscall"
)

var diskIOWorkerLogger = utils.NewLogger("Disk IO Worker", "storage")

const errorIOWorker = "error %v occurred in io-worker %v"

type DiskIOWorker struct {
	workerId int

	iouring *gouring.IoUring

	ShutdownNotifications chan bool
}

func NewIOWorker(id int) *DiskIOWorker {

	ring, err := gouring.New(256, 0)

	if err != nil {

		diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while init iouring for io-worker %v", err, id))
	}

	return &DiskIOWorker{

		workerId: id,

		iouring: ring,

		ShutdownNotifications: make(chan bool, 5),
	}
}

type DiskIOEventBatch struct {
	memoryPool *utils.MemoryPool

	partition *Partition

	waitGroup *sync.WaitGroup

	keyBuffers, valueBuffers, buffers [][]byte

	errs []error

	bytePoolIndex, positionPoolIndex int

	keyElementSize int8

	lookUpWAL bool
}

type DiskIOEvent struct {
	memoryPool *utils.MemoryPool

	partition *Partition

	waitGroup *sync.WaitGroup

	keyBytes, valueBytes, bufferBytes []byte

	err error

	offset uint64

	length int

	lookUpWAL, blob bool
}

func (worker *DiskIOWorker) Start() {

	go func() {

		for {

			select {

			case event := <-diskIOBatchEvents:

				worker.processIOEventBatch(event)

			case event := <-DiskIOEvents:

				worker.processIOEvent(event)

			case <-worker.ShutdownNotifications:

				if worker.iouring != nil {

					worker.iouring.Close()
				}

				diskIOWorkerLogger.Info("shutting down...")

				return
			}
		}

	}()

}

// batch of read request is served here
func (worker *DiskIOWorker) processIOEventBatch(event *DiskIOEventBatch) {

	event.partition.lock.RLock()

	defer event.partition.lock.RUnlock()

	if event.lookUpWAL {

		event.partition.txnLock.RLock()

		defer event.partition.txnLock.RUnlock()
	}

	defer event.waitGroup.Done()

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			for _, index := range event.memoryPool.GetINTPool(event.positionPoolIndex)[:event.keyElementSize] {

				event.errs[index] = errors.New(fmt.Sprintf("error %v occurred while reading in partition %v", err, event.partition.name))
			}

			diskIOWorkerLogger.Error(fmt.Sprintf(errorIOWorker, err, worker.workerId))

			diskIOWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for io-worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	if event.partition.closed {

		for _, index := range event.memoryPool.GetINTPool(event.positionPoolIndex)[:event.keyElementSize] {

			event.errs[index] = errors.New(fmt.Sprintf(utils.ErrorPartitionDeleted, event.partition.name))
		}
		return
	}

	var err error

	requestElementSize, fd, segmentLength := 0, 0, 0

	offsetBytes := event.memoryPool.GetBytePool(event.bytePoolIndex)

	for _, index := range event.memoryPool.GetINTPool(event.positionPoolIndex)[:event.keyElementSize] {

		event.buffers[index], err, fd, segmentLength = event.partition.buildIORequests(event.keyBuffers[index], event.valueBuffers[index], offsetBytes, event.lookUpWAL)

		if err != nil {

			event.errs[index] = err

			continue
		}

		if fd == 0 || segmentLength == 0 { // err from build requests

			event.errs[index] = errors.New(fmt.Sprintf(utils.ErrorIOURing, "prep failed"))

			continue
		}

		if fd == utils.NotAvailable || segmentLength == utils.NotAvailable { // data read from mmap file

			continue
		}

		requestElementSize++

		sqe := worker.iouring.GetSqe()

		sqe.UserData.SetUint64(uint64(index))

		gouring.PrepRead(sqe, fd, &event.valueBuffers[index][0], segmentLength, uint64(codec.ReadINTValue(offsetBytes[2:])))

		event.buffers[index] = event.valueBuffers[index]

		event.errs[index] = errors.New(fmt.Sprintf(utils.ErrorIOURing, "internal"))
	}

	err = nil

	if requestElementSize > 0 {

		err = executeIOURingRequests(event.partition.name, worker.iouring, requestElementSize, event.buffers, event.errs, worker.workerId)

		if err != nil {

			diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while doing io for partition %v", err, event.partition.name))
		}

	}
}

// here one read request is served
func (worker *DiskIOWorker) processIOEvent(event *DiskIOEvent) {

	//in case of blob file this block will be executed
	if event.blob {

		defer event.waitGroup.Done()

		event.bufferBytes, event.err = worker.readBlob(event)

		event.length = 0

		event.blob = false

		event.valueBytes = nil

		event.offset = 0

		return
	}

	event.partition.lock.RLock()

	defer event.partition.lock.RUnlock()

	if event.lookUpWAL {

		event.partition.txnLock.RLock()

		defer event.partition.txnLock.RUnlock()
	}

	defer event.waitGroup.Done()

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			event.err = errors.New(fmt.Sprintf(errorIOWorker, err, worker.workerId))

			diskIOWorkerLogger.Error(fmt.Sprintf(errorIOWorker, err, worker.workerId))

			diskIOWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for io-worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	if event.partition.closed {

		event.err = errors.New(fmt.Sprintf(utils.ErrorPartitionDeleted, event.partition.name))

		return
	}

	_, event.bufferBytes, event.err = event.partition.get(worker.iouring, event.keyBytes, event.valueBytes, event.memoryPool, event.lookUpWAL, worker.workerId)

}

func (worker *DiskIOWorker) readBlob(event *DiskIOEvent) (bytes []byte, err error) {

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			err = errors.New(fmt.Sprintf("error %v occurred while reading in io worker %v", r, worker.workerId))

			diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while reading in io-worker %v", r, worker.workerId))

			diskIOWorkerLogger.Error(fmt.Sprintf("!!!STACK TRACE for reading io-worker %v!!! \n %v", worker.workerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	gouring.PrepRead(worker.iouring.GetSqe(), int(event.partition.blobFile.Fd()), &event.valueBytes[0], event.length, event.offset)

	_, err = worker.iouring.SubmitAndWait(1)

	if err != nil {

		return nil, err
	}

	var cqe *gouring.IoUringCqe

	retries := 1

	for {

		err = worker.iouring.WaitCqe(&cqe)

		if err == syscall.EINTR || err == syscall.EAGAIN || err == syscall.ETIME {

			if utils.DebugEnabled() {

				diskIOWorkerLogger.Warn(fmt.Sprintf("error %v occurred while doing i/o for worker %v, retrying i/o for %v times", err, worker.workerId, retries))
			}

			runtime.Gosched()

			retries++

			continue
		}

		if err != nil {

			diskIOWorkerLogger.Error(fmt.Sprintf("error %v occurred while doing i/o for partition %v, worker %v", err, event.partition.name, worker.workerId))

			return nil, err
		}

		if cqe.Res < 0 {

			err = fmt.Errorf(utils.ErrorIOURing, syscall.Errno(-cqe.Res))

			diskIOWorkerLogger.Error(fmt.Sprintf("%v occurred for partition %v, worker %v", fmt.Sprintf(utils.ErrorIOURing, syscall.Errno(-cqe.Res)), event.partition.name, worker.workerId))

			worker.iouring.SeenCqe(cqe)

			return nil, err
		}

		bytes = event.valueBytes[:cqe.Res]

		worker.iouring.SeenCqe(cqe)

		return bytes, nil
	}
}
