/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-28             Dhaval <PERSON>ra       	MOTADATA-5194  Initial Version
 */

package patchv1_49

import (
	"fmt"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"os"
	"strings"
)

var logger = utils.NewLogger("Patch 1.49", "patch")

func Patch() (err error) {

	dirs, err := os.ReadDir(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir)

	if err != nil {

		return err

	}

	for _, dir := range dirs {

		if strings.Contains(dir.Name(), datastore.RunbookWorklogPlugin) {

			if err := os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + dir.Name()); err != nil {

				logger.Error(fmt.Sprintf("Failed to remove %s", dir.Name()))
			}

		}

	}

	return nil
}
