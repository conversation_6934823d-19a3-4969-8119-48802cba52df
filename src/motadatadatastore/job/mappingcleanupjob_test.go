/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package job

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"strings"
	"testing"
	"time"
)

// Run this test case at first
func TestStartMappingCleanupJob(t *testing.T) {

	//ALways add new columns in the testcases if writing testcases of any other column
	utils.MappingCleanupColumns["protocol"] = []utils.DatastoreType{utils.Flow, utils.FlowAggregation}

	utils.MappingCleanupColumns["tos"] = []utils.DatastoreType{utils.Flow, utils.FlowAggregation}

	assertions := assert.New(t)

	MappingCleanupJobTimer = time.Now().Add(time.Second * 2)

	defer func() {

		MappingCleanupJobTimer = time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day()+1, 05, 00, 00, 00, time.Local).Local()

	}()

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingCleanupJob.Start()

	time.Sleep(time.Second * 3)

	mappingCleanupJob.ShutdownNotifications <- true

	time.Sleep(time.Millisecond * 500)

	bytes, err := utils.ReadLogFile("Mapping Cleanup Job", "job")

	assertions.Nil(err)

	assertions.True(strings.Contains(string(bytes), "shutdown notification received for mapping clean up job"))

	time.Sleep(time.Second * 2)
}

// positive scenario string mappings - Get all mappings from the store
func TestStringMappingCleanupJobType1(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	utils.MappingCleanupColumns["protocol"] = []utils.DatastoreType{utils.Flow, utils.FlowAggregation}

	datastore.AlterIndexableColumns(datastore.FlowPlugin, map[string]interface{}{

		"event.source":         "",
		"protocol":             "",
		"destination.as":       "",
		"source.as":            "",
		"source.threat":        "",
		"destination.threat":   "",
		"destination.ip":       "",
		"source.ip":            "",
		"source.if.index":      "",
		"destination.if.index": "",
		"application":          "",
		"tcp.flags":            "",
		"source.port":          "",
		"destination.port":     "",
		"tos":                  "",
		"source.country":       "",
		"source.city":          "",
		"destination.country":  "",
		"destination.city":     "",
		"source.aso":           "",
		"destination.aso":      "",
		"destination.ip.as":    "",
		"source.domain":        "",
		"destination.domain":   "",
		"source.isp":           "",
		"destination.isp":      ""},
		utils.Add)

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("protocol"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]string, 5)

	ordinals := make([]int32, 5)

	values[0] = "tcp"

	values[1] = "udp"

	values[2] = "udp1"

	values[3] = "tcp2"

	values[4] = "tcp5"

	for _, value := range values {

		err := mappingStore.PutStringMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range ordinals {

		found, ordinal, err := mappingStore.GetStringMapping(values[index])

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-protocol" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-50000-flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	stringMappings := make(utils.MotadataMap)

	mappingStore.ListStringMappings(stringMappings)

	assertions.Equal(len(stringMappings), 5)

}

// positive scenario - Get all mappings from the store , one ordinal is missing
func TestStringMappingCleanupJobType2(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	datastore.AlterIndexableColumns(datastore.FlowPlugin, map[string]interface{}{

		"event.source":         "",
		"protocol":             "",
		"destination.as":       "",
		"source.as":            "",
		"source.threat":        "",
		"destination.threat":   "",
		"destination.ip":       "",
		"source.ip":            "",
		"source.if.index":      "",
		"destination.if.index": "",
		"application":          "",
		"tcp.flags":            "",
		"source.port":          "",
		"destination.port":     "",
		"tos":                  "",
		"source.country":       "",
		"source.city":          "",
		"destination.country":  "",
		"destination.city":     "",
		"source.aso":           "",
		"destination.aso":      "",
		"destination.ip.as":    "",
		"source.domain":        "",
		"destination.domain":   "",
		"source.isp":           "",
		"destination.isp":      ""},
		utils.Add)

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("protocol"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]string, 5)

	ordinals := make([]int32, 5)

	values[0] = "tcp"

	values[1] = "udp"

	values[2] = "udp1"

	values[3] = "tcp2"

	values[4] = "tcp5"

	for _, value := range values {

		err := mappingStore.PutStringMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range values {

		found, ordinal, err := mappingStore.GetStringMapping(values[index])

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	//tcp 5 is removed

	ordinals[4] = 3

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-protocol" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-50000-flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	stringMappings := make(utils.MotadataMap)

	mappingStore.ListStringMappings(stringMappings)

	assertions.Equal(len(stringMappings), 4)

}

// positive scenario numeric mappings  - Get all mappings from the store
func TestNumericMappingCleanupJobType1(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	datastore.AlterIndexableColumns(datastore.FlowPlugin, map[string]interface{}{

		"event.source":         "",
		"protocol":             "",
		"destination.as":       "",
		"source.as":            "",
		"source.threat":        "",
		"destination.threat":   "",
		"destination.ip":       "",
		"source.ip":            "",
		"source.if.index":      "",
		"destination.if.index": "",
		"application":          "",
		"tcp.flags":            "",
		"source.port":          "",
		"destination.port":     "",
		"tos":                  "",
		"source.country":       "",
		"source.city":          "",
		"destination.country":  "",
		"destination.city":     "",
		"source.aso":           "",
		"destination.aso":      "",
		"destination.ip.as":    "",
		"source.domain":        "",
		"destination.domain":   "",
		"source.isp":           "",
		"destination.isp":      ""},
		utils.Add)

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("tos"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]int64, 5)

	ordinals := make([]int32, 5)

	values[0] = 1

	values[1] = 2

	values[2] = 3

	values[3] = 4

	values[4] = 5

	for _, value := range values {

		err := mappingStore.PutNumericMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range ordinals {

		found, ordinal, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-tos" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-50000-flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	for index := range ordinals {

		found, _, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)
	}

}

// positive scenario numeric mappings  - Get all mappings from the store , on ordinal is missing
func TestNumericMappingCleanupJobType2(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	datastore.AlterIndexableColumns(datastore.FlowPlugin, map[string]interface{}{

		"event.source":         "",
		"protocol":             "",
		"destination.as":       "",
		"source.as":            "",
		"source.threat":        "",
		"destination.threat":   "",
		"destination.ip":       "",
		"source.ip":            "",
		"source.if.index":      "",
		"destination.if.index": "",
		"application":          "",
		"tcp.flags":            "",
		"source.port":          "",
		"destination.port":     "",
		"tos":                  "",
		"source.country":       "",
		"source.city":          "",
		"destination.country":  "",
		"destination.city":     "",
		"source.aso":           "",
		"destination.aso":      "",
		"destination.ip.as":    "",
		"source.domain":        "",
		"destination.domain":   "",
		"source.isp":           "",
		"destination.isp":      ""},
		utils.Add)

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("tos"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]int64, 5)

	ordinals := make([]int32, 5)

	values[0] = 1

	values[1] = 2

	values[2] = 3

	values[3] = 4

	values[4] = 5

	for _, value := range values {

		err := mappingStore.PutNumericMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range ordinals {

		found, ordinal, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	ordinals[4] = 0

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-tos" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-50000-flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	mappingStore.Close(encoder)

	datastore.RemoveStore(mappingStore.GetName())

	mappingStore = datastore.GetStore("tos"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	for index := range ordinals {

		found, _, err := mappingStore.GetNumericMapping(values[index], encoder)

		//we have removed that ordinal from the list
		if index == len(ordinals)-1 {

			assertions.False(found)

		} else {

			assertions.True(found)
		}

		assertions.Nil(err)
	}

}

// positive scenario numeric mappings  - Get all mappings from the store , on ordinal is missing from middle
func TestNumericMappingCleanupJobType3(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	datastore.AlterIndexableColumns(datastore.FlowPlugin, map[string]interface{}{

		"event.source":         "",
		"protocol":             "",
		"destination.as":       "",
		"source.as":            "",
		"source.threat":        "",
		"destination.threat":   "",
		"destination.ip":       "",
		"source.ip":            "",
		"source.if.index":      "",
		"destination.if.index": "",
		"application":          "",
		"tcp.flags":            "",
		"source.port":          "",
		"destination.port":     "",
		"tos":                  "",
		"source.country":       "",
		"source.city":          "",
		"destination.country":  "",
		"destination.city":     "",
		"source.aso":           "",
		"destination.aso":      "",
		"destination.ip.as":    "",
		"source.domain":        "",
		"destination.domain":   "",
		"source.isp":           "",
		"destination.isp":      ""},
		utils.Add)

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	utils.MappingCleanupColumns["tos"] = []utils.DatastoreType{utils.Flow, utils.FlowAggregation}

	mappingStore := datastore.GetStore("tos"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]int64, 5)

	ordinals := make([]int32, 5)

	values[0] = 1

	values[1] = 2

	values[2] = 3

	values[3] = 4

	values[4] = 5

	for _, value := range values {

		err := mappingStore.PutNumericMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range ordinals {

		found, ordinal, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	ordinals[2] = 0

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-tos" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-50000-flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	mappingStore.Close(encoder)

	datastore.RemoveStore(mappingStore.GetName())

	mappingStore = datastore.GetStore("tos"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	for index := range ordinals {

		found, _, err := mappingStore.GetNumericMapping(values[index], encoder)

		//we have removed that ordinal from the list
		if index == 2 {

			assertions.False(found)

		} else {

			assertions.True(found)
		}

		assertions.Nil(err)
	}

	availableNumericOrdinals := mappingStore.GetAvailableNumericMappingOrdinals()

	assertions.Equal(availableNumericOrdinals.Count(), 1)

	assertions.True(availableNumericOrdinals.Contains(4))

}

// positive scenario numeric mappings  - Get all mappings from the store , two ordinal is missing from middle anf after that adding two new ordinals
func TestNumericMappingCleanupJobType4(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	datastore.AlterIndexableColumns(datastore.FlowPlugin, map[string]interface{}{

		"event.source":         "",
		"protocol":             "",
		"destination.as":       "",
		"source.as":            "",
		"source.threat":        "",
		"destination.threat":   "",
		"destination.ip":       "",
		"source.ip":            "",
		"source.if.index":      "",
		"destination.if.index": "",
		"application":          "",
		"tcp.flags":            "",
		"source.port":          "",
		"destination.port":     "",
		"tos":                  "",
		"source.country":       "",
		"source.city":          "",
		"destination.country":  "",
		"destination.city":     "",
		"source.aso":           "",
		"destination.aso":      "",
		"destination.ip.as":    "",
		"source.domain":        "",
		"destination.domain":   "",
		"source.isp":           "",
		"destination.isp":      ""},
		utils.Add)

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("tos"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]int64, 5)

	ordinals := make([]int32, 5)

	values[0] = 1

	values[1] = 2

	values[2] = 3

	values[3] = 4

	values[4] = 5

	for _, value := range values {

		err := mappingStore.PutNumericMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range ordinals {

		found, ordinal, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	ordinals[2] = 0

	ordinals[3] = 0

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-tos" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-50000-flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	mappingStore.Close(encoder)

	datastore.RemoveStore(mappingStore.GetName())

	mappingStore = datastore.GetStore("tos"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	for index := range ordinals {

		found, _, err := mappingStore.GetNumericMapping(values[index], encoder)

		//we have removed that ordinal from the list
		if index == 2 || index == 3 {

			assertions.False(found)

		} else {

			assertions.True(found)
		}

		assertions.Nil(err)
	}

	freeNumericMappingOrdinal := mappingStore.GetAvailableNumericMappingOrdinals()

	assertions.Equal(freeNumericMappingOrdinal.Count(), 2)

	assertions.True(freeNumericMappingOrdinal.Contains(4))

	assertions.True(freeNumericMappingOrdinal.Contains(6))

	err = mappingStore.PutNumericMapping(123, encoder)

	assertions.Nil(err)

	found, ordinal, err := mappingStore.GetNumericMapping(123, encoder)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(ordinal, int32(4))

	err = mappingStore.PutNumericMapping(456, encoder)

	assertions.Nil(err)

	found, ordinal, err = mappingStore.GetNumericMapping(456, encoder)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(ordinal, int32(6))

	err = mappingStore.PutNumericMapping(789, encoder)

	assertions.Nil(err)

	found, ordinal, err = mappingStore.GetNumericMapping(789, encoder)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(ordinal, int32(10))

}

// positive scenario - Get all mappings from the store , one ordinal is missing from between
func TestStringMappingCleanupJobType3(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	datastore.AlterIndexableColumns(datastore.FlowPlugin, map[string]interface{}{

		"event.source":         "",
		"protocol":             "",
		"destination.as":       "",
		"source.as":            "",
		"source.threat":        "",
		"destination.threat":   "",
		"destination.ip":       "",
		"source.ip":            "",
		"source.if.index":      "",
		"destination.if.index": "",
		"application":          "",
		"tcp.flags":            "",
		"source.port":          "",
		"destination.port":     "",
		"tos":                  "",
		"source.country":       "",
		"source.city":          "",
		"destination.country":  "",
		"destination.city":     "",
		"source.aso":           "",
		"destination.aso":      "",
		"destination.ip.as":    "",
		"source.domain":        "",
		"destination.domain":   "",
		"source.isp":           "",
		"destination.isp":      ""},
		utils.Add)

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("protocol"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]string, 5)

	ordinals := make([]int32, 5)

	values[0] = "tcp"

	values[1] = "udp"

	values[2] = "udp1"

	values[3] = "tcp2"

	values[4] = "tcp5"

	for _, value := range values {

		err := mappingStore.PutStringMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range ordinals {

		found, ordinal, err := mappingStore.GetStringMapping(values[index])

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	//tcp 5 is removed

	ordinals[2] = 3

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-protocol" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-50000-flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	stringMappings := make(utils.MotadataMap)

	mappingStore.Close(encoder)

	datastore.RemoveStore(mappingStore.GetName())

	mappingStore = datastore.GetStore("protocol"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	mappingStore.ListStringMappings(stringMappings)

	assertions.Equal(len(stringMappings), 4)

	availableStringOrdinals := mappingStore.GetAvailableStringMappingOrdinals()

	assertions.Equal(availableStringOrdinals.Count(), 1)

	assertions.True(availableStringOrdinals.Contains(7))

}

// positive scenario - Get all mappings from the store , two ordinal is missing from between and after that adding 2 ordinals in between
func TestStringMappingCleanupJobType4(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	datastore.AlterIndexableColumns(datastore.FlowPlugin, map[string]interface{}{

		"event.source":         "",
		"protocol":             "",
		"destination.as":       "",
		"source.as":            "",
		"source.threat":        "",
		"destination.threat":   "",
		"destination.ip":       "",
		"source.ip":            "",
		"source.if.index":      "",
		"destination.if.index": "",
		"application":          "",
		"tcp.flags":            "",
		"source.port":          "",
		"destination.port":     "",
		"tos":                  "",
		"source.country":       "",
		"source.city":          "",
		"destination.country":  "",
		"destination.city":     "",
		"source.aso":           "",
		"destination.aso":      "",
		"destination.ip.as":    "",
		"source.domain":        "",
		"destination.domain":   "",
		"source.isp":           "",
		"destination.isp":      ""},
		utils.Add)

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("protocol"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]string, 5)

	ordinals := make([]int32, 5)

	values[0] = "tcp"

	values[1] = "udp"

	values[2] = "udp1"

	values[3] = "tcp2"

	values[4] = "tcp5"

	for _, value := range values {

		err := mappingStore.PutStringMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range ordinals {

		found, ordinal, err := mappingStore.GetStringMapping(values[index])

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	//tcp 5 is removed

	ordinals[2] = 3

	ordinals[3] = 3

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-protocol" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-50000-flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	stringMappings := make(utils.MotadataMap)

	mappingStore.Close(encoder)

	datastore.RemoveStore(mappingStore.GetName())

	mappingStore = datastore.GetStore("protocol"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	mappingStore.ListStringMappings(stringMappings)

	assertions.Equal(len(stringMappings), 3)

	freeStringMappings := mappingStore.GetAvailableStringMappingOrdinals()

	assertions.Equal(freeStringMappings.Count(), 2)

	assertions.True(freeStringMappings.Contains(7))

	assertions.True(freeStringMappings.Contains(9))

	err = mappingStore.PutStringMapping("abc", encoder)

	assertions.Nil(err)

	found, ordinal, err := mappingStore.GetStringMapping("abc")

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(ordinal, int32(7))

	err = mappingStore.PutStringMapping("def", encoder)

	assertions.Nil(err)

	found, ordinal, err = mappingStore.GetStringMapping("def")

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(ordinal, int32(9))

	err = mappingStore.PutStringMapping("ghi", encoder)

	assertions.Nil(err)

	found, ordinal, err = mappingStore.GetStringMapping("ghi")

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(ordinal, int32(13))

}

// positive scenario string mappings - Get all mappings from the store
func TestNumericMappingCleanupJobType5(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("policy.trigger.id"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]int64, 5)

	ordinals := make([]int32, 5)

	values[0] = 1

	values[1] = 2

	values[2] = 3

	values[3] = 4

	values[4] = 5

	for _, value := range values {

		err := mappingStore.PutNumericMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range values {

		found, ordinal, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-policy.trigger.id" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-500007-policy.result", utils.PolicyResult, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	for index := range ordinals {

		found, _, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)
	}

}

// positive scenario numeric mappings  - Get all mappings from the store , on ordinal is missing
func TestNumericMappingCleanupJobType6(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("policy.trigger.id"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]int64, 5)

	ordinals := make([]int32, 5)

	values[0] = 1

	values[1] = 2

	values[2] = 3

	values[3] = 4

	values[4] = 5

	for _, value := range values {

		err := mappingStore.PutNumericMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range values {

		found, ordinal, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	ordinals[4] = 0

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-policy.trigger.id" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-500007-policy.result", utils.PolicyResult, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	mappingStore.Close(encoder)

	datastore.RemoveStore(mappingStore.GetName())

	mappingStore = datastore.GetStore("policy.trigger.id"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	for index := range ordinals {

		found, _, _ := mappingStore.GetNumericMapping(values[index], encoder)

		//we have removed that ordinal from the list
		if index == len(ordinals)-1 {

			assertions.False(found)

		} else {

			assertions.True(found)
		}
	}

}

// positive scenario numeric mappings  - Get all mappings from the store , on ordinal is missing from middle
func TestNumericMappingCleanupJobType7(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("policy.trigger.id"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]int64, 5)

	ordinals := make([]int32, 5)

	values[0] = 1

	values[1] = 2

	values[2] = 3

	values[3] = 4

	values[4] = 5

	for _, value := range values {

		err := mappingStore.PutNumericMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range values {

		found, ordinal, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	ordinals[2] = 0

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-policy.trigger.id" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-500007-policy.result", utils.PolicyResult, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	mappingStore.Close(encoder)

	datastore.RemoveStore(mappingStore.GetName())

	mappingStore = datastore.GetStore("policy.trigger.id"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	for index := range ordinals {

		found, _, _ := mappingStore.GetNumericMapping(values[index], encoder)

		//we have removed that ordinal from the list
		if index == 2 {

			assertions.False(found)

		} else {

			assertions.True(found)
		}
	}

	availableNumericOrdinals := mappingStore.GetAvailableNumericMappingOrdinals()

	assertions.Equal(availableNumericOrdinals.Count(), 1)

	assertions.True(availableNumericOrdinals.Contains(4))

}

// positive scenario numeric mappings  - Get all mappings from the store , two ordinal is missing from middle anf after that adding two new ordinals
func TestNumericMappingCleanupJobType8(t *testing.T) {

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("policy.trigger.id"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]int64, 5)

	ordinals := make([]int32, 5)

	values[0] = 1

	values[1] = 2

	values[2] = 3

	values[3] = 4

	values[4] = 5

	for _, value := range values {

		err := mappingStore.PutNumericMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range values {

		found, ordinal, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	ordinals[2] = 0

	ordinals[3] = 0

	err := mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-policy.trigger.id" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-500007-policy.result", utils.PolicyResult, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	mappingStore.Close(encoder)

	datastore.RemoveStore(mappingStore.GetName())

	mappingStore = datastore.GetStore("policy.trigger.id"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	for index := range ordinals {

		found, _, _ := mappingStore.GetNumericMapping(values[index], encoder)

		//we have removed that ordinal from the list
		if index == 2 || index == 3 {

			assertions.False(found)

		} else {

			assertions.True(found)
		}
	}

	freeNumericMappingOrdinal := mappingStore.GetAvailableNumericMappingOrdinals()

	assertions.Equal(freeNumericMappingOrdinal.Count(), 2)

	assertions.True(freeNumericMappingOrdinal.Contains(4))

	assertions.True(freeNumericMappingOrdinal.Contains(6))

	err = mappingStore.PutNumericMapping(123, encoder)

	assertions.Nil(err)

	found, ordinal, err := mappingStore.GetNumericMapping(123, encoder)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(ordinal, int32(4))

	err = mappingStore.PutNumericMapping(456, encoder)

	assertions.Nil(err)

	found, ordinal, err = mappingStore.GetNumericMapping(456, encoder)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(ordinal, int32(6))

	err = mappingStore.PutNumericMapping(789, encoder)

	assertions.Nil(err)

	found, ordinal, err = mappingStore.GetNumericMapping(789, encoder)

	assertions.True(found)

	assertions.Nil(err)

	assertions.Equal(ordinal, int32(10))

}

func TestQualifyStores(t *testing.T) {

	utils.CleanUpStores()

	assertions := assert.New(t)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	tick := time.Now().UTC()

	dayOne := utils.UnixMillisToDate(tick.UnixMilli())

	dayTwo := utils.UnixMillisToDate(tick.AddDate(0, 0, -1).UnixMilli())

	dayThree := utils.UnixMillisToDate(tick.AddDate(0, 0, -2).UnixMilli())

	dayFour := utils.UnixMillisToDate(tick.AddDate(0, 0, -3).UnixMilli())

	dayFive := utils.UnixMillisToDate(tick.AddDate(0, 0, -4).UnixMilli())

	store := datastore.GetStore(dayOne+utils.HyphenSeparator+datastore.FlowPlugin+"0", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore(dayTwo+utils.HyphenSeparator+datastore.FlowPlugin+"1", utils.FlowAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore(dayThree+utils.HyphenSeparator+datastore.FlowPlugin+"2", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore(dayThree+utils.HyphenSeparator+datastore.FlowPlugin+"21", utils.Log, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore(dayFour+utils.HyphenSeparator+datastore.FlowPlugin+"3", utils.FlowAggregation, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store = datastore.GetStore(dayFive+utils.HyphenSeparator+datastore.FlowPlugin+"4", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	err := mappingCleanupJob.qualifyStores([]utils.DatastoreType{utils.Flow, utils.FlowAggregation})

	assertions.Nil(err)

	assertions.Equal(len(mappingCleanupJob.qualifiedStores), 5)

}

func TestMappingCleanupJobMixedMappings(t *testing.T) {

	assertions := assert.New(t)

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	datastore.AlterIndexableColumns(datastore.FlowPlugin, map[string]interface{}{

		"event.source":         "",
		"protocol":             "",
		"destination.as":       "",
		"source.as":            "",
		"source.threat":        "",
		"destination.threat":   "",
		"destination.ip":       "",
		"source.ip":            "",
		"source.if.index":      "",
		"destination.if.index": "",
		"application":          "",
		"tcp.flags":            "",
		"source.port":          "",
		"destination.port":     "",
		"tos":                  "",
		"source.country":       "",
		"source.city":          "",
		"destination.country":  "",
		"destination.city":     "",
		"source.aso":           "",
		"destination.aso":      "",
		"destination.ip.as":    "",
		"source.domain":        "",
		"destination.domain":   "",
		"source.isp":           "",
		"destination.isp":      ""},
		utils.Add)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("tos"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]int64, 5)

	ordinals := make([]int32, 5)

	values[0] = 1

	values[1] = 2

	values[2] = 3

	values[3] = 4

	values[4] = 5

	for _, value := range values {

		err := mappingStore.PutNumericMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range ordinals {

		found, ordinal, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	err := mappingStore.PutStringMapping("as1", encoder)

	assertions.Nil(err)

	err = mappingStore.PutStringMapping("as2", encoder)

	assertions.Nil(err)

	//Assigning 1 as a string mappings
	ordinals[4] = 3

	//ordinal 3 will be removed

	err = mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	key := "171298767-tos" + utils.OrdinalSuffix

	store := datastore.GetStore("19092024-1-50000-flow", utils.Flow, true, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	for index := range ordinals {

		found, _, err := mappingStore.GetNumericMapping(values[index], encoder)

		//we have removed that ordinal from the list
		if index == len(ordinals)-1 {

			assertions.False(found)

		} else {

			assertions.True(found)
		}

		assertions.Nil(err)
	}

	stringMappings := make(utils.MotadataMap)

	mappingStore.ListStringMappings(stringMappings)

	assertions.Equal(len(stringMappings), 1)

}

// more than one key in the store
func TestMappingCleanupJobMixedMappingsType1(t *testing.T) {

	assertions := assert.New(t)

	utils.MappingCleanupJobs = 1

	datastore.Init()

	utils.CleanUpStores()

	datastore.AlterIndexableColumns(datastore.FlowPlugin, map[string]interface{}{

		"event.source":         "",
		"protocol":             "",
		"destination.as":       "",
		"source.as":            "",
		"source.threat":        "",
		"destination.threat":   "",
		"destination.ip":       "",
		"source.ip":            "",
		"source.if.index":      "",
		"destination.if.index": "",
		"application":          "",
		"tcp.flags":            "",
		"source.port":          "",
		"destination.port":     "",
		"tos":                  "",
		"source.country":       "",
		"source.city":          "",
		"destination.country":  "",
		"destination.city":     "",
		"source.aso":           "",
		"destination.aso":      "",
		"destination.ip.as":    "",
		"source.domain":        "",
		"destination.domain":   "",
		"source.isp":           "",
		"destination.isp":      ""},
		utils.Add)

	mappingCleanupJob := NewMappingCleanUpJob(0)

	mappingStore := datastore.GetStore("tos"+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	assertions.NotNil(mappingStore)

	values := make([]int64, 5)

	ordinals := make([]int32, 5)

	values[0] = 1

	values[1] = 2

	values[2] = 3

	values[3] = 4

	values[4] = 5

	for _, value := range values {

		err := mappingStore.PutNumericMapping(value, encoder)

		assertions.Nil(err)
	}

	for index := range ordinals {

		found, ordinal, err := mappingStore.GetNumericMapping(values[index], encoder)

		assertions.True(found)

		assertions.Nil(err)

		ordinals[index] = ordinal
	}

	err := mappingStore.PutStringMapping("as1", encoder)

	assertions.Nil(err)

	err = mappingStore.PutStringMapping("as2", encoder)

	assertions.Nil(err)

	//Assigning 1 as a string mappings
	ordinals[4] = 3

	//ordinal 3 will be removed

	err = mappingStore.Sync(encoder)

	assertions.Nil(err)

	poolIndex, valueBytes, err := encoder.EncodeINT32Values(ordinals, codec.None, codec.Int32, 32, utils.MaxValueBytes)

	store := datastore.GetStore("19092024-1-50000-flow", utils.Flow, true, true, encoder, tokenizer)

	for index := 0; index < 32; index++ {

		key := fmt.Sprintf("171298767-%dtos"+utils.OrdinalSuffix, index)

		assertions.NotNil(store)

		err = store.Put([]byte(key), valueBytes, encoder, tokenizer)

		assertions.Nil(err)

	}

	encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Sync(encoder)

	assertions.Nil(err)

	store.Close(encoder)

	datastore.RemoveStore(store.GetName())

	err = mappingCleanupJob.cleanup()

	assertions.Nil(err)

	for index := range ordinals {

		found, _, err := mappingStore.GetNumericMapping(values[index], encoder)

		//we have removed that ordinal from the list
		if index == len(ordinals)-1 {

			assertions.False(found)

		} else {

			assertions.True(found)
		}

		assertions.Nil(err)
	}

	stringMappings := make(utils.MotadataMap)

	mappingStore.ListStringMappings(stringMappings)

	assertions.Equal(len(stringMappings), 1)

}

func TestMappingCleanupJobRecover(t *testing.T) {

	assertions := assert.New(t)

	datastore.Init()

	defer func() {

		bytes, err := utils.ReadLogFile("Mapping Cleanup Job", "job")

		assertions.Nil(err)

		assertions.True(strings.Contains(string(bytes), "restarting mapping cleanup job"))
	}()

	utils.MappingCleanupJobs = 2

	mappingCleanupJob := NewMappingCleanUpJob(0)

	defer mappingCleanupJob.recover(time.NewTicker(time.Second))

	panic("err")

}
