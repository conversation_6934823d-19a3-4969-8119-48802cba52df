/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>ata-5190  Migrated constants from datastore package to utils package to match SonarQube Standard
* 2025-04-02			 Dhaval <PERSON>ra			Motadata-4859  Test Case Refactoring
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization

 */

package writer

import (
	"errors"
	"fmt"
	"github.com/dolthub/swiss"
	"github.com/pbnjay/memory"
	"github.com/stretchr/testify/assert"
	"golang.org/x/sys/unix"
	"math"
	"motadatadatastore/cache"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"path/filepath"
	"runtime/debug"
	"strings"
	"sync"
	"testing"
	"time"
)

var (
	metricAggregator *MetricAggregator

	memoryPool = utils.NewMemoryPool(10, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	encoder = codec.NewEncoder(memoryPool)

	decoder = codec.NewDecoder(memoryPool)

	tokenizer *utils.Tokenizer

	syncJobShutdownNotification chan bool

	configWriters []*StaticMetricWriter
)

func TestMain(m *testing.M) {

	for _, arg := range os.Args {

		if strings.Contains(arg, "bench") {

			utils.EnvironmentType = utils.DatastoreBenchUnitEnvironment

			for i := 0; i < 3; i++ {

				writer := NewStaticMetricWriter(i)

				writer.Start()

				configWriters = append(configWriters, writer)
			}

			break
		}

	}

	err := os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.LogDirectory + utils.PathSeparator)

	configBytes, err := os.ReadFile(filepath.Dir(filepath.Dir(filepath.Dir(filepath.Dir(utils.CurrentDir)))) + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {
		panic(err)
	}

	if utils.InitConfigs(utils.UpdateConfigs(configBytes, utils.MotadataMap{
		"datastore.store.sync.timer.seconds": 10,
		"system.log.level":                   0,
		"datastore.flush.timer.seconds":      2,
	})) {

		tokenizer = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}

		memoryBytes := int64(memory.TotalMemory())

		debug.SetMemoryLimit((memoryBytes * 85) / 100)

		utils.CleanUpStores() //comment this for debugging

		cache.InitCacheEngine()

		utils.StoreSyncJobAddNotifications = make(chan string, 1_00_000) //comment this for debugging

		syncJobShutdownNotification = make(chan bool)

		go startSyncJob(utils.StoreSyncJobAddNotifications)

		utils.MetricAggregationRequests = make([]chan utils.MetricAggregationRequest, 1)

		utils.DiskIOWorkers = 300

		ioWorkers := make([]*storage.DiskIOWorker, utils.DiskIOWorkers)

		for id := range ioWorkers {

			ioWorkers[id] = storage.NewIOWorker(id)

			ioWorkers[id].Start()
		}

		metricAggregator = NewMetricAggregator(0)

		utils.MetricAggregationRequests[0] = metricAggregator.Requests

		utils.VerticalAggregationSyncTimerSeconds = 5

		utils.MetricAggregators = 1

		metricAggregator.Start()

		datastore.Init()

		utils.SystemBootSequence = utils.Datastore

		utils.VerticalWriters = 1

		utils.HorizontalWriters = 1

		utils.StaticMetricWriters = 1

		utils.IndexWriters = 4

		mockMetricTokenizer = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}

		utils.VerticalFormatSyncNotifications = make(chan utils.WriterSyncEvent, 100)

		utils.HorizontalFormatSyncNotifications = make(chan utils.WriterSyncEvent, 100)

		staticMetricWriter = NewStaticMetricWriter(0)

		horizontalWriter = NewHorizontalWriter(0)

		verticalWriter = NewVerticalWriter(0, []*StaticMetricWriter{staticMetricWriter})

		indexWriters = make([]*IndexWriter, utils.IndexWriters)

		utils.IndexWriterRequests = make([]chan utils.IndexEvent, utils.IndexWriters)

		for id := 0; id < len(indexWriters); id++ {

			indexWriters[id] = NewIndexWriter(id)

			utils.IndexWriterRequests[id] = indexWriters[id].Events

			indexWriters[id].Start()
		}

		horizontalWriter.Start()

		staticMetricWriter.Start()

		verticalWriter.Start()

		utils.SetLogLevel(0)

		utils.Create(metricAggregatorDir)

		m.Run()

		for _, worker := range ioWorkers {

			worker.ShutdownNotifications <- true
		}
	}
}

func TestProbeNumericMetricv1(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.memory.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(1))

	//int8 --> int16-->int32-->int64 --> float64

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int16), int64(129))

	tick += 5

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int32), int64(math.MaxInt16+2))

	tick += 5

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+2))

	tick += 5

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+4))

	tick += 5

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxInt32+4+0.05)

	tick += 5

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "80%")

	tick += 5

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxInt32+4+0.05)

	tick += 5

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	time.Sleep(time.Second * 13)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// sum count min max last object id
	assertions.Equal(6, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Count + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.Equal(int8(7), values[0])

}

func TestProbeNumericMetricv3(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.disk.used.percent"

	plugin := "20-server"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int32), int64(math.MaxInt16+2))

	tick += 5

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	//second monitor data in float

	objectId = 2

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxInt16+2.24)

	tick += 10

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	// third monitor data in string

	objectId = 3

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), codec.ToString(math.MaxInt16+2.24))

	tick += 10

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	time.Sleep(time.Second * 8)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// sum count min max last object id
	assertions.Equal(6, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Count + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	//value[0] - object 0 value[1] = object 1
	for _, value := range values {

		assertions.Equal(int8(1), value)
	}

}

func TestProbeNumericMetricv4(t *testing.T) {

	utils.CleanUpStores()

	utils.Create(metricAggregatorDir)

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.memory.percent2"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(1))

	//int8 --> int16-->int32-->int64 --> float64

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	//

	objectId = 2

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(2))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	//

	objectId = 3

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(3))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	// again same tick

	objectId = 1

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(8))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 2

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "67%")

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 3

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(2))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	time.Sleep(time.Second * 3)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// sum count min max last object id
	assertions.Equal(6, len(keyBuffers))

	//decode objectId

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.ObjectId + utils.OrdinalSuffix + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, objects, err := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.Int32, valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	//decode min
	keyBytes = []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Min + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes = encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err = store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.NotNil(values)

	//resolve mapping

	store = datastore.GetStore(utils.ObjectId+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, false, true, encoder, tokenizer)

	assertions.NotNil(store)

	err, dataType, mapperPoolIndex := store.ResolveMapping(poolIndex, encoder, metricAggregator.stringMappings, metricAggregator.numericMappings, len(objects))

	assertions.Nil(err)

	assertions.Equal(codec.Int64, dataType)

	defer encoder.MemoryPool.ReleaseINT64Pool(mapperPoolIndex)

	resolvedObjects := encoder.MemoryPool.GetINT64Pool(mapperPoolIndex)

	result := make(map[int64]int8)

	for index := range resolvedObjects {

		result[resolvedObjects[index]] = values[index]
	}

	originalResult := make(map[int64]int8)

	originalResult[3] = 2

	originalResult[1] = 1

	originalResult[2] = 2

	assertions.Equal(result, originalResult)

	//assert garbage store

	storeName = utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + utils.Garbage + utils.HyphenSeparator + "5"

	store = datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	assertions.NotNil(store)

}

func TestProbeNumericMetricv5(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.memory.percent3"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "1%")

	//int8 --> int16-->int32-->int64 --> float64

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	//

	objectId = 2

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "2%")

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	//

	objectId = 3

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "4%")

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	// again same tick

	objectId = 1

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(8))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 2

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "67%")

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 3

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(2))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	time.Sleep(time.Second * 3)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// sum count min max last object id
	assertions.Equal(6, len(keyBuffers))

	//decode objectId

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.ObjectId + utils.OrdinalSuffix + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, objects, err := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.Int32, valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	//decode min
	keyBytes = []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Min + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes = encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err = store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.NotNil(values)

	//resolve mapping

	store = datastore.GetStore(utils.ObjectId+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, false, true, encoder, tokenizer)

	assertions.NotNil(store)

	err, dataType, mapperPoolIndex := store.ResolveMapping(poolIndex, encoder, metricAggregator.stringMappings, metricAggregator.numericMappings, len(objects))

	assertions.Nil(err)

	assertions.Equal(codec.Int64, dataType)

	defer encoder.MemoryPool.ReleaseINT64Pool(mapperPoolIndex)

	resolvedObjects := encoder.MemoryPool.GetINT64Pool(mapperPoolIndex)

	result := make(map[int64]int8)

	for index := range resolvedObjects {

		result[resolvedObjects[index]] = values[index]
	}

	originalResult := make(map[int64]int8)

	originalResult[1] = 8

	originalResult[3] = 2

	assertions.Equal(result, originalResult)

	//assert garbage store

	storeName = utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + utils.Garbage + utils.HyphenSeparator + "5"

	store = datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	assertions.NotNil(store)

}

func TestProbeNumericMetricv6(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.memory.percent4"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "1%")

	//int8 --> int16-->int32-->int64 --> float64

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	//

	objectId = 2

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "2%")

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	//

	objectId = 3

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "4%")

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	// again same tick

	objectId = 1

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), float64(8))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 2

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "67%")

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 3

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), float64(2))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	time.Sleep(time.Second * 3)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// sum count min max last object id
	assertions.Equal(6, len(keyBuffers))

	//decode objectId

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.ObjectId + utils.OrdinalSuffix + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, objects, err := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.Int32, valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	//decode min
	keyBytes = []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Min + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes = encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err = store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.NotNil(values)

	//resolve mapping

	store = datastore.GetStore(utils.ObjectId+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, false, true, encoder, tokenizer)

	assertions.NotNil(store)

	err, dataType, mapperPoolIndex := store.ResolveMapping(poolIndex, encoder, metricAggregator.stringMappings, metricAggregator.numericMappings, len(objects))

	assertions.Nil(err)

	assertions.Equal(codec.Int64, dataType)

	defer encoder.MemoryPool.ReleaseINT64Pool(mapperPoolIndex)

	resolvedObjects := encoder.MemoryPool.GetINT64Pool(mapperPoolIndex)

	result := make(map[int64]float64)

	for index := range resolvedObjects {

		result[resolvedObjects[index]] = values[index]
	}

	originalResult := make(map[int64]float64)

	originalResult[3] = 2

	originalResult[1] = 8

	assertions.Equal(result, originalResult)

	//assert garbage store

	storeName = utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + utils.Garbage + utils.HyphenSeparator + "5"

	store = datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	assertions.NotNil(store)

}

func TestProbeNumericMetricv7(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.memory.percent5"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "1%")

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	//

	objectId = 2

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "2%")

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	//

	objectId = 3

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "4%")

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	// again same tick

	objectId = 1

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), float64(8))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 2

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "67%")

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 3

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), float64(2))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	// again same tick

	objectId = 1

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(30))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 2

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(56))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 3

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(2))

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	time.Sleep(time.Second * 3)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// sum count min max last object id
	assertions.Equal(6, len(keyBuffers))

	//decode objectId

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.ObjectId + utils.OrdinalSuffix + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, objects, err := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.Int32, valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	//decode min
	keyBytes = []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Min + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes = encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err = store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.NotNil(values)

	//resolve mapping

	store = datastore.GetStore(utils.ObjectId+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, false, true, encoder, tokenizer)

	assertions.NotNil(store)

	err, dataType, mapperPoolIndex := store.ResolveMapping(poolIndex, encoder, metricAggregator.stringMappings, metricAggregator.numericMappings, len(objects))

	assertions.Nil(err)

	assertions.Equal(codec.Int64, dataType)

	defer encoder.MemoryPool.ReleaseINT64Pool(mapperPoolIndex)

	resolvedObjects := encoder.MemoryPool.GetINT64Pool(mapperPoolIndex)

	result := make(map[int64]float64)

	for index := range resolvedObjects {

		result[resolvedObjects[index]] = values[index]
	}

	originalResult := make(map[int64]float64)

	originalResult[3] = 2

	originalResult[1] = 8

	originalResult[2] = 56

	assertions.Equal(result, originalResult)

	//assert garbage store

	storeName = utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + utils.Garbage + utils.HyphenSeparator + "5"

	store = datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	assertions.NotNil(store)

}

func TestPanicAggregator(t *testing.T) {

	assertions := assert.New(t)

	assertions.True(len(utils.MetricAggregationRequests) > 0)

	utils.MetricAggregationRequests[0] <- utils.MetricAggregationRequest{

		Entries: nil, StoreType: "", Key: "", Plugin: "", Column: "",
	}

	time.Sleep(time.Second * 2)

	metricAggregator.merge("13", "0-avai", "tick", "5", "cpu", int(utils.PerformanceMetric), true, false)

	time.Sleep(time.Second * 2)

	assertions.True(metricAggregator.pendingProbe)
}

// ######################## after shutting down the aggregator #####################################

func TestInitWalFiles(t *testing.T) {

	assertions := assert.New(t)

	metricAggregator.ShutdownNotifications <- true

	time.Sleep(time.Second * 1)

	metricAggregator := NewMetricAggregator(1)

	metricAggregator.stringMappings = swiss.NewMap[int32, []int](1000)

	metricAggregator.numericMappings = swiss.NewMap[int32, []int](1000)

	for i := range metricAggregator.valueBuffers {

		bytes, err := unix.Mmap(-1, 0, utils.MaxValueBufferBytes, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

		assertions.Nil(err)

		metricAggregator.valueBuffers[i] = bytes
	}

	txnBufferBytes, err := unix.Mmap(-1, 0, 10*1024*1024, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		metricAggregator.logger.Error(fmt.Sprintf("error %v occurred while mapping annonymous txn buffer for metric aggregator %v", err, metricAggregator.aggregatorId))

		txnBufferBytes = make([]byte, 10*1024*1024)
	}

	metricAggregator.txnBufferBytes = txnBufferBytes

	for i := range metricAggregator.events {

		metricAggregator.events[i] = storage.DiskIOEventBatch{}
	}

	for i := range metricAggregator.tokenizers {

		metricAggregator.tokenizers[i] = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}
	}

	bytes, err := unix.Mmap(-1, 0, utils.GetDataWriterValueBufferBytes(), unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		bytes = make([]byte, utils.GetDataWriterValueBufferBytes())
	}

	metricAggregator.bufferBytes = bytes

	metricAggregator.stringMappings = swiss.NewMap[int32, []int](1000)

	metricAggregator.numericMappings = swiss.NewMap[int32, []int](1000)

	for i := range metricAggregator.valueBuffers {

		metricAggregator.valueBuffers[i] = make([]byte, utils.MaxValueBufferBytes)
	}

	for i := range metricAggregator.events {

		metricAggregator.events[i] = storage.DiskIOEventBatch{}
	}

	for i := range metricAggregator.tokenizers {

		metricAggregator.tokenizers[i] = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}
	}

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "dummy.metric.testing.init.wal.files"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+1))

	tick += 5

	utils.OverflowLength = 9000

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	datastore.UpdateVerticalAggregations(metric, true)

	metricAggregator = NewMetricAggregator(0)

	_ = assertions

	metricAggregator.Start()

	defer func() {

		metricAggregator.ShutdownNotifications <- true
	}()

	time.Sleep(time.Second * 3)

	roundedTick := fmt.Sprintf("%d", utils.RoundOffSeconds(tick, 5))

	storeName := utils.SecondsToDate(utils.StringToInt32(roundedTick)) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	assertions.NotNil(store)

	err = store.Sync(encoder)

	assertions.Nil(err)

	valueBytes := make([]byte, 100000)

	keyBytes := []byte(roundedTick + utils.KeySeparator + plugin + utils.KeySeparator + utils.ObjectId + utils.OrdinalSuffix + utils.KeySeparator + "0")

	wg := sync.WaitGroup{}

	found, data, err := store.Get(keyBytes, valueBytes, encoder, storage.DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.NotNil(data)
}

func TestProbeNumericMetricv2(t *testing.T) {

	assertions := assert.New(t)

	metricAggregator = NewMetricAggregator(1)

	metricAggregator.stringMappings = swiss.NewMap[int32, []int](1000)

	metricAggregator.numericMappings = swiss.NewMap[int32, []int](1000)

	for i := range metricAggregator.valueBuffers {

		bytes, err := unix.Mmap(-1, 0, utils.MaxValueBufferBytes, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

		assertions.Nil(err)

		metricAggregator.valueBuffers[i] = bytes
	}

	txnBufferBytes, err := unix.Mmap(-1, 0, 10*1024*1024, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		metricAggregator.logger.Error(fmt.Sprintf("error %v occurred while mapping annonymous txn buffer for metric aggregator %v", err, metricAggregator.aggregatorId))

		txnBufferBytes = make([]byte, 10*1024*1024)
	}

	metricAggregator.txnBufferBytes = txnBufferBytes

	for i := range metricAggregator.events {

		metricAggregator.events[i] = storage.DiskIOEventBatch{}
	}

	for i := range metricAggregator.tokenizers {

		metricAggregator.tokenizers[i] = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}
	}

	bytes, err := unix.Mmap(-1, 0, utils.GetDataWriterValueBufferBytes(), unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		bytes = make([]byte, utils.GetDataWriterValueBufferBytes())
	}

	metricAggregator.bufferBytes = bytes

	metricAggregator.stringMappings = swiss.NewMap[int32, []int](1000)

	metricAggregator.numericMappings = swiss.NewMap[int32, []int](1000)

	for i := range metricAggregator.valueBuffers {

		metricAggregator.valueBuffers[i] = make([]byte, utils.MaxValueBufferBytes)
	}

	for i := range metricAggregator.events {

		metricAggregator.events[i] = storage.DiskIOEventBatch{}
	}

	for i := range metricAggregator.tokenizers {

		metricAggregator.tokenizers[i] = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}
	}

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.percent"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(1))

	//int8 --> int16-->int32-->int64 --> float64

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int16), int64(129))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int32), int64(math.MaxInt16+2))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+2))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+4))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), float64(math.MaxInt32+4)+0.05)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	////

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "80%")

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), float64(math.MaxInt32+4)+0.05)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// sum count min max last object id
	assertions.Equal(6, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Count + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.Equal(int8(7), values[0])

}

func TestOverflowReadBufferBytesSync(t *testing.T) {

	bufferBytes := metricAggregator.bufferBytes

	defer func() {

		metricAggregator.bufferBytes = bufferBytes
	}()

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "system.cpu.kernel.percent"

	//int8 --> int16-->int32-->int64 --> float64

	plugin := "201-server"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(1))

	//int8 --> int16-->int32-->int64 --> float64

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	metricAggregator.bufferBytes = make([]byte, 10)

	flush(metricAggregator)

	metricAggregator.bufferBytes = bufferBytes

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int16), int64(129))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	metricAggregator.bufferBytes = make([]byte, 10)

	flush(metricAggregator)

	metricAggregator.bufferBytes = bufferBytes

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int32), int64(math.MaxInt16+2))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	metricAggregator.bufferBytes = make([]byte, 10)

	flush(metricAggregator)

	metricAggregator.bufferBytes = bufferBytes

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+2))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	metricAggregator.bufferBytes = make([]byte, 10)

	flush(metricAggregator)

	metricAggregator.bufferBytes = bufferBytes

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+4))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	metricAggregator.bufferBytes = make([]byte, 10)

	flush(metricAggregator)

	metricAggregator.bufferBytes = bufferBytes

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), float64(math.MaxInt32+4)+0.05)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	metricAggregator.bufferBytes = make([]byte, 10)

	flush(metricAggregator)

	metricAggregator.bufferBytes = bufferBytes

	////

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "80%")

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	metricAggregator.bufferBytes = make([]byte, 10)

	flush(metricAggregator)

	metricAggregator.bufferBytes = bufferBytes

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), float64(math.MaxInt32+4)+0.05)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	metricAggregator.bufferBytes = make([]byte, 10)

	flush(metricAggregator)

	metricAggregator.bufferBytes = bufferBytes

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// sum count min max last object id
	assertions.Equal(6, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Count + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.Equal(int8(7), values[0])

}

func TestProbeStringMetricv1(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~alias.name"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "loopback")

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + utils.Garbage + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id and object
	assertions.Equal(3, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeStringValues(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Equal(len(values), 1)

	assertions.Equal("loopback", values[0])

}

func TestProbeStringMetricv3(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~alias.name"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "loopback")

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + utils.Garbage + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id and object
	assertions.Equal(3, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeStringValues(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Equal(1, len(values))

	assertions.Equal("loopback", values[0])

}

func TestProbeStringMetricv2(t *testing.T) {

	time.Sleep(time.Second * 1)

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~description"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "description1")

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 5

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "description1")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + utils.Garbage + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id and object
	assertions.Equal(3, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeStringValues(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Equal(2, len(values))

	assertions.Equal([]string{"description1", "description1"}, values)

}

// for bug fix, in append method, when we append new aggregation value while the old value is still present in that part
func TestAppendWithInstanceMissing(t *testing.T) {

	aggregator := NewMetricAggregator(21)

	aggregator.shutdown = true

	aggregator.Start()

	utils.WriterEngineShutdownMutex.Add(1)

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "dummy.metric.testing.append"

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(1))

	//int8 --> int16-->int32-->int64 --> float64

	plugin := "20-server"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 2

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 3

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 4

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 5

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	for metric := range aggregator.timers {

		aggregator.sync(metric, true)

	}

	objectId = 6

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 7

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 8

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 9

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 10

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 11

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 12

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 13

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 14

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	objectId = 15

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "")

	aggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	for metric := range aggregator.timers {

		key = metric

		aggregator.sync(metric, true)

	}

	utils.Split(key, utils.HyphenSeparator, aggregator.tokenizers[0])

	roundedTick := aggregator.tokenizers[0].Tokens[aggregator.tokenizers[0].Counts-5]

	interval := aggregator.tokenizers[0].Tokens[aggregator.tokenizers[0].Counts-4]

	keyBytes := []byte(roundedTick + utils.KeySeparator + plugin + utils.KeySeparator + "sum" + utils.KeySeparator + "0")

	storeName := utils.SecondsToDate(utils.StringToInt32(roundedTick)) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + interval

	store := datastore.GetStore(storeName, utils.None, false, true, encoder, aggregator.tokenizers[1])

	assertions := assert.New(t)

	assertions.NotNil(store)

	valueBytes := make([]byte, 1000)

	found, buffBytes, err := store.Get(keyBytes, valueBytes, encoder, aggregator.event, aggregator.waitGroup, aggregator.tokenizers[1], true)

	assertions.True(found)

	assertions.NotNil(buffBytes)

	assertions.Nil(err)

	index, decodedValue, err := decoder.DecodeINT8Values(codec.GetEncoding(buffBytes[0]), buffBytes[1:], string(keyBytes), storeName, 0)

	assertions.Nil(err)

	decoder.MemoryPool.ReleaseINT8Pool(index)

	assertions.Equal(15, len(decodedValue))

}

func TestMetricAggregatorMangerNotification(t *testing.T) {

	assertions := assert.New(t)

	tick := time.Now().Unix()

	baseTick := utils.GetBaseTickv1(tick)

	_ = os.RemoveAll(utils.JobDir)

	_ = os.MkdirAll(utils.JobDir, os.ModePerm)

	utils.ManagerNotifications = make(chan utils.MotadataMap, 1000)

	maxInterval := utils.AggregationIntervals[len(utils.AggregationIntervals)-1]

	minInterval := utils.AggregationIntervals[0]

	startTick := baseTick + int64(maxInterval)*2

	startTick += int64(minInterval)

	metricAggregator := NewMetricAggregator(1)

	metricAggregator.shutdown = true

	metricAggregator.Start()

	metric := "testing.manager.notification.2"

	plugin := "109-dummy.plugin"

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], utils.UnixToSeconds(startTick))

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+1))

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	startTick += int64(minInterval * 60)

	datastore.UpdateAggregationContexts(plugin+utils.AggregationSeparator+metric+utils.KeySeparator+codec.INT64ToStringValue(utils.GetBaseTickv1(startTick))+utils.KeySeparator+codec.INTToStringValue(maxInterval)+utils.KeySeparator+utils.VerticalFormat, utils.GetPosition(startTick, maxInterval), utils.Add)

	for metric := range metricAggregator.timers {

		metricAggregator.sync(metric, true)
	}

	datastore.UpdateAggregationContexts(plugin+utils.AggregationSeparator+metric+utils.KeySeparator+codec.INT64ToStringValue(utils.GetBaseTickv1(startTick))+utils.KeySeparator+codec.INTToStringValue(minInterval)+utils.KeySeparator+utils.VerticalFormat, utils.GetPosition(startTick, minInterval), utils.Add)

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], utils.UnixToSeconds(startTick))

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+1))

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	assertions.Len(metricAggregator.files, 0)

	context := <-utils.ManagerNotifications

	assertions.Equal(context.GetInt64Value(utils.Tick), utils.SecondsToUnix(utils.RoundOffSeconds(utils.UnixToSeconds(startTick), maxInterval)))

	assertions.Equal(context.GetStringValue(utils.Interval), codec.INTToStringValue(maxInterval))

	context = <-utils.ManagerNotifications

	assertions.Equal(context.GetInt64Value(utils.Tick), utils.SecondsToUnix(utils.RoundOffSeconds(utils.UnixToSeconds(startTick), minInterval)))

	assertions.Equal(context.GetStringValue(utils.Interval), codec.INTToStringValue(minInterval))

	context = <-utils.ManagerNotifications

	assertions.Equal(context.GetInt64Value(utils.Tick), utils.SecondsToUnix(utils.RoundOffSeconds(utils.UnixToSeconds(startTick), maxInterval)))

	assertions.Equal(context.GetStringValue(utils.Interval), codec.INTToStringValue(maxInterval))

}

// previous int8 next int8
func TestProbeINT8Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(123))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	tick += 60

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]int8{123, 123}, values)

}

// previous int16 next int16
func TestProbeINT16Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets1"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int16), int64(300))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	tick += 60

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT16Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]int16{300, 300}, values)

}

// previous int32 next int32
func TestProbeINT32Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets2"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int32), int64(math.MaxInt16+1))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	tick += 60

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT32Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]int32{math.MaxInt16 + 1, math.MaxInt16 + 1}, values)

}

// previous int64 next int64
func TestProbeINT64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets3"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+1))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT64Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]int64{math.MaxInt32 + 1, math.MaxInt32 + 1}, values)

}

func TestProbeINT64ToFloat64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~err.packets"

	plugin := "20-network"

	// one monitor data in int

	objectId := 10

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt64))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Sum + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	if codec.GetDataType(valueBytes[0]) == codec.Int64 {

		poolIndex := -1

		var values []int64

		poolIndex, values, err = decoder.DecodeINT64Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], "", storeName, 0)

		defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

		assertions.Equal(2, len(values))

		//for object instance 1###x and 1###y

		assertions.Equal([]int64{math.MaxInt64, math.MaxInt64}, values)
	} else {

		poolIndex := -1

		var values []float64

		poolIndex, values, err = decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

		defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

		assertions.Equal(2, len(values))

		//for object instance 1###x and 1###y

		assertions.Equal([]float64{math.MaxInt64, math.MaxInt64}, values)
	}

}

// previous float8 next int8
func TestProbeFloat8INT8Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets4"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), 123+0.09)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(123))

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{123 + 0.09, 123}, values)

}

// previous float16 next int8
func TestProbeFloat16INT8Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets5"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), 300+0.09)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(123))

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT16Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{300 + 0.09, 123}, values)

}

// previous float64 next int8
func TestProbeFloat64INT8Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets6"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxFloat32+0.09)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(123))

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{math.MaxFloat32 + 0.09, 123}, values)

}

// previous Int8 new Int64
func TestProbeINT8INT64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets7"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(127))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+1))

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT64Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]int64{127, math.MaxInt32 + 1}, values)

}

// previous Int16 new Int64
func TestProbeINT16INT64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets8"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int16), int64(300))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+1))

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT64Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]int64{300, math.MaxInt32 + 1}, values)

}

// previous Int32 new Int64
func TestProbeINT32INT64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets9"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int32), int64(math.MaxInt16+1))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+1))

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT64Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]int64{math.MaxInt16 + 1, math.MaxInt32 + 1}, values)

}

// previous Int64 new Int32
func TestProbeINT64INT32Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets10"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+1))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int32), int64(math.MaxInt16+1))

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT64Values(codec.GetEncoding(valueBytes[0]), codec.GetDataType(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]int64{math.MaxInt32 + 1, math.MaxInt16 + 1}, values)

}

//Float Combinations

// previous float8 next float8
func TestProbeFLOAT8Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets11"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float8), 123+0.01)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{123.01, 123.01}, values)

}

// cover int16 part of the count
func TestProbeCountINT16Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets11"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float8), 123+0.01)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "30"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 30)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Count + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.Equal(2, len(values))

	int16Values := make([]int16, 2)

	int16Values[0] = math.MaxInt16

	int16Values[1] = math.MaxInt16

	poolIndex, valueBytes, err = encoder.EncodeINT16Values(codec.None, int16Values, utils.MaxValueBytes)

	assertions.Nil(err)

	valueBytes[0] = byte(codec.Int8) | byte(codec.GetEncoding(valueBytes[0]))

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId+1), metric, "y")

	for index := 0; index < 3600; index++ {

		event := utils.MetricAggregationRequest{

			Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
		}

		metricAggregator.writeWAL(event)

	}
	flush(metricAggregator)

	poolIndex, valueBytes = encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	found, valueBytes, err = store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, true)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

}

// previous float16 next float16
func TestProbeFLOAT16Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets12"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float16), 300+0.01)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT16Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{300.01, 300.01}, values)

}

// previous float64 next float64
func TestProbeFLOAT64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets13"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxInt32+0.01)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{math.MaxInt32 + 0.01, math.MaxInt32 + 0.01}, values)

}

// previous int8 next float64
func TestProbeINT8FLOAT64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets14"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int8), int64(123))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxInt32+0.01)

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{123, math.MaxInt32 + 0.01}, values)

}

// previous int16 next float64
func TestProbeINT16FLOAT64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets15"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int16), int64(300))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxInt32+0.01)

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{300, math.MaxInt32 + 0.01}, values)

}

// previous int32 next float64
func TestProbeINT32FLOAT64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets16"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int32), int64(math.MaxInt16+1))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxInt32+0.01)

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{math.MaxInt16 + 1, math.MaxInt32 + 0.01}, values)

}

// previous int64 next float64
func TestProbeINT64FLOAT64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets17"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+1))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxInt32+0.01)

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT64Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{math.MaxInt32 + 1, math.MaxInt32 + 0.01}, values)

}

// previous float64 next string
func TestProbeFloat64StringValues(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets18"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxFloat32+0.09)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "loopback0")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + utils.Garbage + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object last
	assertions.Equal(3, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeStringValues(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Equal(1, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]string{"loopback0"}, values)

}

//overflow scenarios

func TestProbeFLOAT8ValueOverflowed(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	utils.OverflowLength = 1

	metric := "interface~in.packets19"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float8), 123+0.01)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last, different parts
	assertions.Equal(14, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeFLOAT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	assertions.Equal(1, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]float64{123.01}, values)

	parts := store.GetMaxPart(utils.GetHash64([]byte(codec.INT32ToStringValue(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin)))

	assertions.Equal(uint16(1), parts)

	utils.OverflowLength = utils.MaxPoolLength

}

func TestProbeStringValueOverflowed(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	utils.OverflowLength = 1

	metric := "interface~alias.name1"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.String), "loopback0")

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + utils.Garbage + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , different parts
	assertions.Equal(6, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Last + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeStringValues(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseStringPool(poolIndex)

	assertions.Equal(1, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]string{"loopback0"}, values)

	parts := store.GetMaxPart(utils.GetHash64([]byte(codec.INT32ToStringValue(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin)))

	assertions.Equal(uint16(1), parts)

	utils.OverflowLength = utils.MaxPoolLength

}

//Count Scenario

// previous int64 next float64
func TestProbeINT16ToINT8FLOAT64Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets20"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Int64), int64(math.MaxInt32+1))

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//change count value manually

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "5"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	poolIndex, valueBytes, err := encoder.EncodeINT16Values(codec.None, []int16{300}, utils.MaxValueBytes)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Count + datastore.DefaultKeyPart)

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	//again sending data
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	entries = make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float64), math.MaxInt32+0.01)

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	store = datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes = []byte(codec.ToString(utils.RoundOffSeconds(tick, 5)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Count + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes = encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT16Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	assertions.Equal(2, len(values))

	//for object instance 1###x and 1###y

	assertions.Equal([]int16{300, 1}, values)

}

// cover int32 part of the count
func TestProbeCountINT32Values(t *testing.T) {

	tick := utils.RoundOffSeconds(utils.UnixToSeconds(time.Now().Unix()), 5)

	metric := "interface~in.packets89"

	plugin := "20-network"

	// one monitor data in int

	objectId := 1

	key := datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "x")

	entries := make([][]interface{}, 2)

	entries[0] = append(entries[0], tick)

	entries[1] = append(append(entries[1], codec.Float8), 123+0.01)

	tick += 5

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	assertions := assert.New(t)

	// assert 5 minutes store

	datastore.Close()

	datastore.Init()

	//merge and append function coverage
	tick += 60

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId), metric, "y")

	metricAggregator.writeWAL(utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	})

	flush(metricAggregator)

	datastore.Close()

	datastore.Init()

	storeName := utils.SecondsToDate(tick) + utils.HyphenSeparator + datastore.HorizontalStore + utils.HyphenSeparator + metric + utils.HyphenSeparator + "30"

	store := datastore.GetStore(storeName, utils.MetricAggregation, false, true, encoder, tokenizer)

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	assertions.Nil(err)

	// last object id ,object , sum , count , min , max last
	assertions.Equal(7, len(keyBuffers))

	//assert count

	keyBytes := []byte(codec.ToString(utils.RoundOffSeconds(tick, 30)) + utils.KeySeparator + plugin + utils.KeySeparator + utils.Count + datastore.DefaultKeyPart)

	valuePoolIndex, valueBytes := encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(valuePoolIndex)

	found, valueBytes, err := store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	poolIndex, values, err := decoder.DecodeINT8Values(codec.GetEncoding(valueBytes[0]), valueBytes[1:], "", storeName, 0)

	defer decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	assertions.True(len(values) > 1)

	int32values := make([]int32, 2)

	int32values[0] = math.MaxInt16

	int32values[1] = math.MaxInt16

	poolIndex, valueBytes, err = encoder.EncodeINT32Values(int32values, codec.None, codec.Int24, 32, utils.MaxValueBytes)

	assertions.Nil(err)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	err = store.Put(keyBytes, valueBytes, encoder, tokenizer)

	assertions.Nil(err)

	key = datastore.GetMetricKey(codec.INTToStringValue(objectId+1), metric, "y")

	overflowLength := utils.OverflowLength

	defer func() {

		utils.OverflowLength = overflowLength
	}()
	utils.OverflowLength = math.MaxInt32

	event := utils.MetricAggregationRequest{

		Entries: entries, StoreType: codec.INTToStringValue(int(utils.PerformanceMetric)), Column: metric, Key: key, Plugin: plugin,
	}

	for index := 0; index < math.MaxInt16+1; index++ {

		metricAggregator.writeWAL(event)

	}

	flush(metricAggregator)

	poolIndex, valueBytes = encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

	found, valueBytes, err = store.Get(keyBytes, valueBytes, encoder, metricAggregator.event, metricAggregator.waitGroup, tokenizer, true)

	assertions.True(found)

	assertions.NotNil(valueBytes)

	assertions.Nil(err)

}

func TestReadWalError(t *testing.T) {

	assert.Equal(t, errors.New("failed to read file"), metricAggregator.readWAL([]byte("")))
}

func TestToInit64Values(t *testing.T) {

	aggregator := NewMetricAggregator(17)

	aggregator.Start()

	defer func() {

		aggregator.ShutdownNotifications <- true

	}()

	valueBuffers := [][]byte{{1, 0, 0}}

	store, err := storage.OpenOrCreateStore("test-aggregator-2", utils.None, encoder, tokenizer, false)

	assert.Nil(t, err)

	previousDataType := codec.Int8

	currentDataType := codec.Int8

	err, _ = aggregator.toINT64Values(valueBuffers, 0, store, previousDataType, currentDataType, "", "", 1)

	assert.NotNil(t, err)

	previousDataType = codec.Int16

	err, _ = aggregator.toINT64Values(valueBuffers, 0, store, previousDataType, currentDataType, "", "", 1)

	assert.NotNil(t, err)

	previousDataType = codec.Int32

	err, _ = aggregator.toINT64Values(valueBuffers, 0, store, previousDataType, currentDataType, "", "", 1)

	assert.NotNil(t, err)

	previousDataType = codec.Int64

	err, _ = aggregator.toINT64Values(valueBuffers, 0, store, previousDataType, currentDataType, "", "", 1)

	assert.NotNil(t, err)

}

func TestWriteGroupV2(t *testing.T) {

	mappingStore := datastore.GetStore(datastore.Object+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	aggregator := NewMetricAggregator(17)

	aggregator.Start()

	defer func() {

		aggregator.ShutdownNotifications <- true

	}()

	objects := swiss.NewMap[string, int](0)

	mappingStore.Close(encoder)

	aggregator.writeGroups(objects, "", 0, nil, true, "", 0)

	assert.True(t, aggregator.pendingProbe)

}

func TestWriteGroupV3(t *testing.T) {

	mappingStore := datastore.GetStore(utils.ObjectId+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, encoder, tokenizer)

	aggregator := NewMetricAggregator(17)

	aggregator.Start()

	defer func() {

		aggregator.ShutdownNotifications <- true

	}()

	objects := swiss.NewMap[string, int](0)

	mappingStore.Close(encoder)

	aggregator.writeGroups(objects, "", 0, nil, false, "", 0)

	assert.True(t, aggregator.pendingProbe)

}

func TestWriteGroupV4(t *testing.T) {

	aggregator := NewMetricAggregator(17)

	aggregator.Start()

	defer func() {

		aggregator.ShutdownNotifications <- true

	}()

	storeDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + utils.ObjectId + utils.HyphenSeparator + datastore.Mappings

	_, err := os.Stat(storeDir)

	if os.IsNotExist(err) {

		os.MkdirAll(storeDir, 0755)
	}

	corruptedStore := storeDir + "-corrupted" + utils.HyphenSeparator + utils.INT64ToStringValue(time.Now().UnixMilli())

	_, err = os.Stat(corruptedStore)

	if os.IsNotExist(err) {

		os.Mkdir(corruptedStore, 0755)
	}

	objects := swiss.NewMap[string, int](0)

	defer func() {

		if err := recover(); err != nil {

			os.Remove(storeDir)

			os.Remove(corruptedStore)

		} else {

			assert.True(t, aggregator.pendingProbe)

			os.Remove(storeDir)

			os.Remove(corruptedStore)
		}

	}()

	aggregator.writeGroups(objects, "", 0, nil, false, "", 0)

}

func TestMetricAggregatorWriteTxnV1(t *testing.T) {

	assertions := assert.New(t)

	aggregator := NewMetricAggregator(1)

	aggregator.Start()

	time.Sleep(time.Millisecond * 100)

	keyBytes := []byte("dummy-Key-v1")

	valueBytes := []byte("12345678dummy-value-v1")

	store, err := storage.OpenOrCreateStore("dummy-store-type-2", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	for i := 0; i < 1000000; i++ {

		err = aggregator.writeTxn(keyBytes, valueBytes)

		assertions.Nil(err)
	}

	aggregator.txnPartition = store.GetPartition(keyBytes, aggregator.tokenizers[1])

	commited := aggregator.commit(store, 0, "0")

	assertions.True(commited)

	err = store.Sync(encoder)

	assertions.Nil(err)

	assertions.Nil(err)

	wg := sync.WaitGroup{}

	valueBytes = make([]byte, 100000)

	found, data, err := store.Get(keyBytes, valueBytes, encoder, storage.DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(data, []byte("dummy-value-v1"))

	utils.AssertLogMessage(assertions, fmt.Sprintf("Metric Aggregator-%d", 1), "aggregator", "remapping annonymous txn buffers with current length")
}

func TestEventAggregatorWriteTxnV2(t *testing.T) {

	assertions := assert.New(t)

	aggregator := NewEventAggregator(1)

	aggregator.Start()

	time.Sleep(time.Millisecond * 100)

	keyBytes := []byte("dummy-Key-v1")

	valueBytes := []byte("12345678dummy-value-v1")

	store, err := storage.OpenOrCreateStore("dummy-store-type-2", utils.None, encoder, tokenizer, false)

	assertions.Nil(err)

	for i := 0; i < 1000000; i++ {

		err = aggregator.writeTxn(keyBytes, valueBytes)

		assertions.Nil(err)
	}

	aggregator.txnPartition = store.GetPartition(keyBytes, aggregator.tokenizers[1])

	err = aggregator.commit(store)

	err = store.Sync(encoder)

	assertions.Nil(err)

	assertions.Nil(err)

	wg := sync.WaitGroup{}

	valueBytes = make([]byte, 100000)

	found, data, err := store.Get(keyBytes, valueBytes, encoder, storage.DiskIOEvent{}, &wg, tokenizer, false)

	assertions.Nil(err)

	assertions.True(found)

	assertions.Equal(data, []byte("dummy-value-v1"))

	utils.AssertLogMessage(assertions, fmt.Sprintf("Event Aggregator %v", 1), "aggregator", "remapping annonymous txn buffers with current length")

}

func TestMetricAggregatorV1(t *testing.T) {

	assertions := assert.New(t)

	metricAggregator := NewMetricAggregator(0)

	maxBufferBytes := utils.MaxValueBufferBytes

	writerTxnBufferBytes := utils.GetDataWriterTxnBufferBytes()

	writerValueBufferBytes := utils.GetDataWriterValueBufferBytes()

	defer func() {

		utils.MaxValueBufferBytes = maxBufferBytes

		utils.DataWriterTxnBufferBytes = writerTxnBufferBytes

		utils.DataWriterValueBufferBytes = writerValueBufferBytes

	}()

	utils.DataWriterTxnBufferBytes = 0

	utils.DataWriterValueBufferBytes = 0

	utils.MaxValueBufferBytes = 0

	datastore.UpdateVerticalAggregations("dummy.metric", true)

	os.RemoveAll(metricAggregatorDir + "dummy.metric" + utils.PathSeparator + "dummy")

	os.MkdirAll(metricAggregatorDir+"dummy.metric", os.ModePerm)

	os.Create(metricAggregatorDir + "dummy.metric" + utils.PathSeparator + "dummy-1-2-3")

	os.MkdirAll(metricAggregatorDir+"dummy.metric"+utils.PathSeparator+"dummy.metric-1-2-3", os.ModePerm)

	metricAggregator.Start()

	time.Sleep(time.Millisecond * 100)

	metricAggregator.timers["dummy"] = 0

	metricAggregator.ShutdownNotifications <- true

	time.Sleep(time.Second * 1)

	utils.AssertLogMessage(assertions, fmt.Sprintf("Metric Aggregator-%v", 0), "aggregator", "occurred while mapping annonymous buffer for metric aggregator")

}

func TestMetricAggregatorV2(t *testing.T) {

	assertions := assert.New(t)

	metricAggregator := NewMetricAggregator(1)

	metricAggregator.Start()

	entries := [][]interface{}{{}, {codec.Invalid}}

	os.RemoveAll(metricAggregatorDir)

	metricAggregator.writeWAL(utils.MetricAggregationRequest{
		Entries: entries,
		Column:  "dummy",
	})

	os.MkdirAll(metricAggregatorDir, os.ModePerm)

	currentTime := time.Now()

	entries = [][]interface{}{{int32(currentTime.Unix()), int32(currentTime.Unix() + 1), int32(currentTime.Unix() + 2)}, {codec.Int64, int64(1), int64(1), int64(1)}}

	plugin := "demo-plugin"

	tick := utils.SecondsToUnix(int32(currentTime.Unix()))

	key := plugin + utils.AggregationSeparator + "dummy" + utils.KeySeparator + codec.INT64ToStringValue(utils.GetBaseTickv1(tick)) + utils.KeySeparator + codec.INTToStringValue(utils.AggregationIntervals[0]) + utils.KeySeparator + utils.VerticalFormat

	datastore.UpdateAggregationContexts(key, 0, utils.Add)

	metricAggregator.writeWAL(utils.MetricAggregationRequest{
		Entries: entries,
		Column:  "dummy",
		Plugin:  plugin,
	})

	utils.AssertLogMessage(assertions, fmt.Sprintf("Metric Aggregator-%v", 1), "aggregator", "occurred while creating partition for metric")

}

func TestMetricAggregatorV3(t *testing.T) {

	assertions := assert.New(t)

	metricAggregator := NewMetricAggregator(1)

	metricAggregator.Start()

	metric := fmt.Sprintf("dummy.metric-%d-1-2-plugin-5", time.Now().Unix())

	os.MkdirAll(metricAggregatorDir+metric, os.ModePerm)

	os.Create(metricAggregatorDir + metric + utils.PathSeparator + metric)

	file, err := os.OpenFile(metricAggregatorDir+metric+metric, os.O_WRONLY|os.O_CREATE, os.ModePerm)

	file.Write([]byte("1234567890"))

	assertions.NoError(err)

	metricAggregator.files[metric] = file

	metricAggregator.sync(metric, false)

	os.Remove(metricAggregatorDir + metric + utils.PathSeparator + metric)

	file, err = os.Create(metricAggregatorDir + metric + utils.PathSeparator + metric)

	metricAggregator.files[metric] = file

	metricAggregator.sync(metric, false)

	metricAggregator.readWAL([]byte{0, 0, 0, 10})

	metricAggregator.readWAL(append([]byte{byte(len(utils.EOTBytes)), 0, 0, 0}, 0, 0, 0))

	utils.AssertLogMessage(assertions, fmt.Sprintf("Metric Aggregator-%v", 1), "aggregator", "failed to read wal file")

}

func flush(aggregator *MetricAggregator) {

	for metric := range aggregator.timers {

		aggregator.sync(metric, true)

	}
}

func startSyncJob(notifications chan string) {

	testEncoder := codec.NewEncoder(utils.NewMemoryPool(5, 100_000, true, utils.DefaultBlobPools))

	stores := make(map[string]struct{})

	timer := time.NewTicker(time.Second * 5)

	for {

		if utils.GlobalShutdown {

			return
		}

		select {

		case store := <-notifications:

			tokens := strings.Split(store, utils.GroupSeparator)

			stores[tokens[0]] = struct{}{}

		case <-timer.C:

			for storeName := range stores {

				if utils.GlobalShutdown {

					return
				}

				store := datastore.GetStore(storeName, utils.None, false, true, testEncoder, tokenizer)

				if store == nil {

					continue
				}

				err := store.Sync(testEncoder)

				if err != nil && !strings.Contains(err.Error(), "partition is closed") {

					panic(err)
				}

			}

		case <-syncJobShutdownNotification:
			return

		}
	}

}
