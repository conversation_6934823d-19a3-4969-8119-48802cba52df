{"floating.columns": {}, "garbage.columns": {}, "indexable.columns": {"500001-trap": {"event.source": "", "trap.oid": "", "trap.vendor": "", "trap.severity": ""}, "500000-flow": {"event.source": "", "protocol": "", "destination.as": "", "source.as": "", "source.threat": "", "destination.threat": "", "destination.ip": "", "source.ip": "", "source.if.index": "", "destination.if.index": "", "application": "", "tcp.flags": "", "source.port": "", "destination.port": "", "tos": "", "source.country": "", "source.city": "", "destination.country": "", "destination.city": "", "source.aso": "", "destination.aso": "", "destination.ip.as": "", "source.domain": "", "destination.domain": "", "source.isp": "", "destination.isp": "", "user": ""}, "500002-audit": {"event.source": "", "audit.module": "", "audit.operation": "", "audit.user": "", "audit.status": "", "audit.remote.ip": ""}, "500003-policy": {"object.id": "", "policy.type": "", "severity": "", "policy.id": "", "object.category": "", "instance": "", "metric": "", "event.source": ""}, "500004-policy": {"policy.type": "", "severity": "", "policy.id": "", "event.source": "", "event.field": ""}, "500005-policy": {"policy.type": "", "severity": "", "policy.id": "", "event.source": "", "event.field": ""}, "500010-policy": {"event.field": "", "event.source": "", "policy.id": "", "policy.type": "", "severity": ""}, "500014-health.metric": {"event.source": "", "engine.category": "", "engine.type": ""}, "500015-health.metric": {"event.source": ""}, "500016-health.metric": {"event.source": ""}, "500020-health.metric": {"event.source": ""}, "500021-health.metric": {"event.source": "", "jvm.gc.name": ""}, "500007-policy.result": {"policy.trigger.id": "", "severity": ""}, "500008-notification": {"event.source": "", "user.notification.type": "", "user.notification.severity": ""}, "490000-correlated.metric": {"event.source": ""}, "499998-policy.flap": {"object.id": "", "severity": "", "policy.id": "", "instance": "", "event.source": ""}, "500009-other": {"event.source": "", "event.source.type": "", "event.pattern.id": "", "event.category": "", "event.severity": ""}, "500011-log.stat": {"event.source": "", "event.source.type": "", "event.category": ""}, "500018-flow": {"event.source": ""}, "499999-event.history": {"event.source": "", "event.source.type": "", "event.pattern.id": "", "event.category": "", "event.severity": ""}, "500019-config": {"event.source": ""}, "500022-runbook.worklog": {"event.source": "", "runbook.worklog.id": "", "runbook.worklog.type": "", "object.id": "", "policy.id": "", "instance": "", "netroute.id": ""}, "500023-compliance": {"compliance.policy.id": ""}, "500027-policy": {"netroute.id": "", "policy.id": "", "severity": "", "event.field": "", "policy.type": ""}, "500028-policy.flap": {"netroute.id": "", "policy.id": "", "severity": ""}}, "searchable.columns": {"message": {}, "interface": {}}, "column.encoders": {"audit.message": 4, "message": 4, "user.notification.message": 4, "trap.message": 4, "trap.raw.message": 4, "policy.message": 4, "event": 4, "config.operation.output": 4, "runbook.worklog.result": 4, "runbook.worklog.error": 4, "config.event": 4}, "blob.columns": {"message": {}, "trap.message": {}, "trap.raw.message": {}, "audit.message": {}, "policy.message": {}, "event": {}, "user.notification.message": {}, "config.operation.output": {}, "config.event": {}, "runbook.worklog.result": {}, "runbook.worklog.error": {}, "netroute.event": {}}}