{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659378600304, "to.datetime": 1659464999304, "duration": 86399}, "visualization.category": "TopN", "visualization.type": "Chart", "visualization.properties": {"chart": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "interface~in.packets.sum", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}], "sorting": {"limit": 5, "order": "asc", "column": "interface~in.packets.last"}}}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "de7f92a8-d413-46d7-a908-a8507530e9eb", "session-id": "e04a5a90-62cd-4723-bcce-46a55f29798a", "user.name": "admin", "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "interface~in.packets", "aggregator": "last", "entity.type": "all", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "entity.keys": {"1^x^interface~in.packets": "2000-interface", "2^x^interface~in.packets": "2000-interface", "3^x^interface~in.packets": "2000-interface", "4^x^interface~in.packets": "2000-interface", "5^x^interface~in.packets": "2000-interface", "1^y^interface~in.packets": "2000-interface", "2^y^interface~in.packets": "2000-interface", "3^y^interface~in.packets": "2000-interface", "4^y^interface~in.packets": "2000-interface", "5^y^interface~in.packets": "2000-interface", "1^z^interface~in.packets": "2000-interface", "2^z^interface~in.packets": "2000-interface", "3^z^interface~in.packets": "2000-interface", "4^z^interface~in.packets": "2000-interface", "5^z^interface~in.packets": "2000-interface", "1^w^interface~in.packets": "2000-interface", "2^w^interface~in.packets": "2000-interface", "3^w^interface~in.packets": "2000-interface", "4^w^interface~in.packets": "2000-interface", "5^w^interface~in.packets": "2000-interface", "1^a^interface~in.packets": "2000-interface", "2^a^interface~in.packets": "2000-interface", "3^a^interface~in.packets": "2000-interface", "4^a^interface~in.packets": "2000-interface", "5^a^interface~in.packets": "2000-interface", "6^x^interface~in.packets": "2000-interface", "7^x^interface~in.packets": "2000-interface", "8^x^interface~in.packets": "2000-interface", "9^x^interface~in.packets": "2000-interface", "10^x^interface~in.packets": "2000-interface", "6^y^interface~in.packets": "2000-interface", "7^y^interface~in.packets": "2000-interface", "8^y^interface~in.packets": "2000-interface", "9^y^interface~in.packets": "2000-interface", "10^y^interface~in.packets": "2000-interface", "6^z^interface~in.packets": "2000-interface", "7^z^interface~in.packets": "2000-interface", "8^z^interface~in.packets": "2000-interface", "9^z^interface~in.packets": "2000-interface", "10^z^interface~in.packets": "2000-interface", "6^w^interface~in.packets": "2000-interface", "7^w^interface~in.packets": "2000-interface", "8^w^interface~in.packets": "2000-interface", "9^w^interface~in.packets": "2000-interface", "10^w^interface~in.packets": "2000-interface", "6^a^interface~in.packets": "2000-interface", "7^a^interface~in.packets": "2000-interface", "8^a^interface~in.packets": "2000-interface", "9^a^interface~in.packets": "2000-interface", "10^a^interface~in.packets": "2000-interface"}, "plugins": ["2000-interface"]}, {"data.point": "interface~out.packets", "aggregator": "last", "entity.type": "all", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "entity.keys": {"1^x^interface~out.packets": "2000-interface", "2^x^interface~out.packets": "2000-interface", "3^x^interface~out.packets": "2000-interface", "4^x^interface~out.packets": "2000-interface", "5^x^interface~out.packets": "2000-interface", "1^y^interface~out.packets": "2000-interface", "2^y^interface~out.packets": "2000-interface", "3^y^interface~out.packets": "2000-interface", "4^y^interface~out.packets": "2000-interface", "5^y^interface~out.packets": "2000-interface", "1^z^interface~out.packets": "2000-interface", "2^z^interface~out.packets": "2000-interface", "3^z^interface~out.packets": "2000-interface", "4^z^interface~out.packets": "2000-interface", "5^z^interface~out.packets": "2000-interface", "1^w^interface~out.packets": "2000-interface", "2^w^interface~out.packets": "2000-interface", "3^w^interface~out.packets": "2000-interface", "4^w^interface~out.packets": "2000-interface", "5^w^interface~out.packets": "2000-interface", "1^a^interface~out.packets": "2000-interface", "2^a^interface~out.packets": "2000-interface", "3^a^interface~out.packets": "2000-interface", "4^a^interface~out.packets": "2000-interface", "5^a^interface~out.packets": "2000-interface", "6^x^interface~out.packets": "2000-interface", "7^x^interface~out.packets": "2000-interface", "8^x^interface~out.packets": "2000-interface", "9^x^interface~out.packets": "2000-interface", "10^x^interface~out.packets": "2000-interface", "6^y^interface~out.packets": "2000-interface", "7^y^interface~out.packets": "2000-interface", "8^y^interface~out.packets": "2000-interface", "9^y^interface~out.packets": "2000-interface", "10^y^interface~out.packets": "2000-interface", "6^z^interface~out.packets": "2000-interface", "7^z^interface~out.packets": "2000-interface", "8^z^interface~out.packets": "2000-interface", "9^z^interface~out.packets": "2000-interface", "10^z^interface~out.packets": "2000-interface", "6^w^interface~out.packets": "2000-interface", "7^w^interface~out.packets": "2000-interface", "8^w^interface~out.packets": "2000-interface", "9^w^interface~out.packets": "2000-interface", "10^w^interface~out.packets": "2000-interface", "6^a^interface~out.packets": "2000-interface", "7^a^interface~out.packets": "2000-interface", "8^a^interface~out.packets": "2000-interface", "9^a^interface~out.packets": "2000-interface", "10^a^interface~out.packets": "2000-interface"}, "plugins": ["2000-interface"]}], "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "status": [], "instance.type": "interface", "plugins": ["2000-interface"]}, "admin.role": "yes", "query.id": 116611606211068, "sub.query.id": 116611606211069}