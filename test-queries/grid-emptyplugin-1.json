{"visualization.timeline": {"relative.timeline": "today", "visualization.time.range.inclusive": "no", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1667759400206, "to.datetime": 1667845799206, "duration": 86399}, "visualization.properties": {"grid": {"highchart.settings": {}, "chart.legend": "no", "sorting": {"limit": 10, "order": "desc"}, "chart.label": "no"}}, "ui.event.uuid": "2dc0ff4b-0135-4163-b70d-3b9a47dcf7e9", "user.name": "admin", "id": -1, "visualization.category": "Grid", "uuid": null, "visualization.type": "Area", "session-id": "61086cfc-b38e-46a2-865f-b1ef999d56c3", "visualization.data.sources": {"visualization.result.by": ["audit.module"], "filters": {"result.filter": {}, "data.filter": {}}, "type": "log", "data.points": [{"aggregator": "", "data.point": "audit.module", "entity.type": "all", "plugins": ["21001-audit"], "entities": {"************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit"}}, {"aggregator": "", "data.point": "audit.operation", "entity.type": "all", "plugins": ["21001-audit"], "entities": {"************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit"}}, {"aggregator": "", "data.point": "audit.user", "entity.type": "all", "plugins": ["21001-audit"], "entities": {"************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit"}}, {"aggregator": "", "data.point": "audit.message", "entity.type": "all", "plugins": ["21001-audit"], "entities": {"************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit"}}, {"aggregator": "", "data.point": "audit.status", "entity.type": "all", "plugins": ["21001-audit"], "entities": {"************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit"}}, {"aggregator": "", "data.point": "audit.remote.ip", "entity.type": "all", "plugins": ["21001-audit"], "entities": {"************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit"}}], "entities": {"************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit", "************": "21001-audit"}}, "admin.role": "yes", "query.id": 27850310404451, "sub.query.id": 27850310404452}