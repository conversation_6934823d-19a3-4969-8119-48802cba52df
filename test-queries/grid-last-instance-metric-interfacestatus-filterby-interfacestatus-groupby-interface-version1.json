{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659292200617, "to.datetime": 1659378599617, "duration": 86399}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "visualization.result.by": ["interface"], "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "7b04220d-ccdf-46da-aea8-f230e85964dc", "session-id": "a0b81062-80a4-47cd-8bd3-4a95d0c83145", "user.name": "admin", "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "conditions": [{"value": "Up", "operand": "interface~status^last", "operator": "="}], "operator": "and"}]}, "result.filter": {}}, "visualization.result.by": ["monitor", "interface"], "data.points": [{"data.point": "interface~status", "aggregator": "last", "entity.type": "Monitor", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "entity.keys": {"1^x^interface~status": "2000-interface", "1^y^interface~status": "2000-interface", "1^z^interface~status": "2000-interface", "1^a^interface~status": "2000-interface", "1^w^interface~status": "2000-interface", "2^x^interface~status": "2000-interface", "2^y^interface~status": "2000-interface", "2^z^interface~status": "2000-interface", "2^a^interface~status": "2000-interface", "2^w^interface~status": "2000-interface", "3^x^interface~status": "2000-interface", "3^y^interface~status": "2000-interface", "3^z^interface~status": "2000-interface", "3^a^interface~status": "2000-interface", "3^w^interface~status": "2000-interface", "4^x^interface~status": "2000-interface", "4^y^interface~status": "2000-interface", "4^z^interface~status": "2000-interface", "4^a^interface~status": "2000-interface", "4^w^interface~status": "2000-interface", "5^x^interface~status": "2000-interface", "5^y^interface~status": "2000-interface", "5^z^interface~status": "2000-interface", "5^a^interface~status": "2000-interface", "5^w^interface~status": "2000-interface"}, "plugins": ["2000-interface"]}], "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface"}, "status": [], "instance.type": "interface", "plugins": ["2000-interface"]}, "admin.role": "yes", "query.id": 74205653047123, "sub.query.id": 74205653047124}